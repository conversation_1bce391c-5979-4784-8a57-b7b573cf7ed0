CREATE TABLE IF NOT EXISTS marketing.google_ads_campaigns (
    campaign_status String,
    campaign String,
    budget Nullable(Float64),
    budget_name String,
    budget_type String,
    status String,
    status_reasons String,
    optimization_score Nullable(Float64),
    currency_code String,
    cost Nullable(Float64),
    campaign_type String,
    avg_cpv Nullable(Float64),
    interactions Nullable(UInt64),
    interaction_rate Nullable(Float64),
    avg_cost Nullable(Float64),
    ctr Nullable(Float64),
    engagement_rate Nullable(Float64),
    invalid_interaction_rate Nullable(Float64),
    general_invalid_click_rate Nullable(Float64),
    avg_target_cpa Nullable(Float64),
    view_rate_instream Nullable(Float64),
    view_rate_infeed Nullable(Float64),
    view_rate_shorts Nullable(Float64),
    impr_coviewed Nullable(UInt64),
    impr_abs_top_pct Nullable(Float64),
    watch_time Nullable(Float64),
    avg_target_roas Nullable(Float64),
    avg_cpe Nullable(Float64),
    general_invalid_clicks Nullable(UInt64),
    invalid_clicks Nullable(UInt64),
    invalid_click_rate Nullable(Float64),
    avg_cpm Nullable(Float64),
    avg_target_cost_inapp Nullable(Float64),
    avg_watch_time_per_impr Nullable(Float64),
    impr_top_pct Nullable(Float64),
    views Nullable(UInt64),
    invalid_interactions Nullable(UInt64),
    engagements Nullable(UInt64),
    all_video_ad_seq_impr Nullable(UInt64),
    impr Nullable(UInt64),
    bid_strategy_type String,
    clicks Nullable(UInt64),
    conv_rate Nullable(Float64),
    conversions Nullable(UInt64),
    avg_cpc Nullable(Float64),
    cost_per_conv Nullable(Float64)
) ENGINE = MergeTree()
ORDER BY campaign;
