#!/bin/bash
set -e

RAW_FILE="/data/google_ads.csv"
CLEAN_FILE="/data/google_ads_clean.csv"

echo "🧹 Cleaning raw CSV file..."

cat "$RAW_FILE" \
  | sed 's/--/\\N/g' \                                # replace -- with NULL
  | sed 's/%//g' \                                    # remove %
  | sed 's/"//g' \                                    # strip quotes
  | sed -E 's/([0-9]),([0-9]{3})/\1\2/g' \            # remove thousands separators
  > "$CLEAN_FILE.tmp"

echo "📂 Fixing header row..."

sed -i '1s/.*/campaign_status,campaign,budget,budget_name,budget_type,status,status_reasons,optimization_score,currency_code,cost,campaign_type,avg_cpv,interactions,interaction_rate,avg_cost,ctr,engagement_rate,invalid_interaction_rate,general_invalid_click_rate,avg_target_cpa,view_rate_instream,view_rate_infeed,view_rate_shorts,impr_coviewed,impr_abs_top_pct,watch_time,avg_target_roas,avg_cpe,general_invalid_clicks,invalid_clicks,invalid_click_rate,avg_cpm,avg_target_cost_inapp,avg_watch_time_per_impr,impr_top_pct,views,invalid_interactions,engagements,all_video_ad_seq_impr,impr,bid_strategy_type,clicks,conv_rate,conversions,avg_cpc,cost_per_conv/' "$CLEAN_FILE.tmp"

mv "$CLEAN_FILE.tmp" "$CLEAN_FILE"
cat "$CLEAN_FILE"

echo "✅ Header fixed, importing into ClickHouse..."

clickhouse-client --query="
    CREATE DATABASE IF NOT EXISTS marketing;

    CREATE TABLE IF NOT EXISTS marketing.google_ads_campaigns
    (
        campaign_status String,
        campaign String,
        budget Float64,
        budget_name String,
        budget_type String,
        status String,
        status_reasons String,
        optimization_score Float64,
        currency_code String,
        cost Float64,
        campaign_type String,
        avg_cpv Float64,
        interactions UInt64,
        interaction_rate Float64,
        avg_cost Float64,
        ctr Float64,
        engagement_rate Float64,
        invalid_interaction_rate Float64,
        general_invalid_click_rate Float64,
        avg_target_cpa Float64,
        view_rate_instream Float64,
        view_rate_infeed Float64,
        view_rate_shorts Float64,
        impr_coviewed UInt64,
        impr_abs_top_pct Float64,
        watch_time Float64,
        avg_target_roas Float64,
        avg_cpe Float64,
        general_invalid_clicks UInt64,
        invalid_clicks UInt64,
        invalid_click_rate Float64,
        avg_cpm Float64,
        avg_target_cost_inapp Float64,
        avg_watch_time_per_impr Float64,
        impr_top_pct Float64,
        views UInt64,
        invalid_interactions UInt64,
        engagements UInt64,
        all_video_ad_seq_impr UInt64,
        impr UInt64,
        bid_strategy_type String,
        clicks UInt64,
        conv_rate Float64,
        conversions UInt64,
        avg_cpc Float64,
        cost_per_conv Float64
    )
    ENGINE = MergeTree()
    ORDER BY campaign_status;
"

clickhouse-client --query="INSERT INTO marketing.google_ads_campaigns FORMAT CSVWithNames" < "$CLEAN_FILE"

echo "🎉 Import completed successfully!"
