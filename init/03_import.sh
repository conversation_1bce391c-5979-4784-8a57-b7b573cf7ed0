#!/bin/bash
set -e

RAW_FILE="/data/google_ads.csv"
CLEAN_FILE="/data/google_ads_clean.csv"


echo "🧹 Cleaning raw CSV file..."

# Process the CSV file step by step
# First, handle thousands separators within quoted numbers, then filter out summary rows
cat "$RAW_FILE" | \
  sed 's/--/\\N/g' | \
  sed 's/%//g' | \
  sed -E 's/"([0-9]+),([0-9]{3})"/"\1\2"/g' | \
  sed -E 's/"([0-9]+),([0-9]{3})"/"\1\2"/g' | \
  sed 's/"//g' | \
  grep -v "^Total:" \
  > "$CLEAN_FILE.tmp"

echo "📂 Fixing header row..."

sed -i '1s/.*/campaign_status,campaign,budget,budget_name,budget_type,status,status_reasons,optimization_score,currency_code,cost,campaign_type,avg_cpv,interactions,interaction_rate,avg_cost,ctr,engagement_rate,invalid_interaction_rate,general_invalid_click_rate,avg_target_cpa,view_rate_instream,view_rate_infeed,view_rate_shorts,impr_coviewed,impr_abs_top_pct,watch_time,avg_target_roas,avg_cpe,general_invalid_clicks,invalid_clicks,invalid_click_rate,avg_cpm,avg_target_cost_inapp,avg_watch_time_per_impr,impr_top_pct,views,invalid_interactions,engagements,all_video_ad_seq_impr,impr,bid_strategy_type,clicks,conv_rate,conversions,avg_cpc,cost_per_conv/' "$CLEAN_FILE.tmp"

mv "$CLEAN_FILE.tmp" "$CLEAN_FILE"

echo "✅ Header fixed, importing into ClickHouse..."

clickhouse-client --query="INSERT INTO marketing.google_ads_campaigns FORMAT CSVWithNames" < "$CLEAN_FILE"

echo "🎉 Import completed successfully!"
