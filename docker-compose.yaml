services:
  clickhouse:
    image: clickhouse/clickhouse-server:24
    container_name: clickhouse
    ports:
      - "8123:8123" # HTTP
      - "9000:9000" # Native TCP
    volumes:
      - ./init:/docker-entrypoint-initdb.d
      - ./data:/data
    environment:
      CLICKHOUSE_DB: marketing
      CLICKHOUSE_USER: default
      CLICKHOUSE_PASSWORD: ""
    ulimits:
      nofile:
        soft: 262144
        hard: 262144
